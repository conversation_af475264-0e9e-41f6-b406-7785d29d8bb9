{"name": "node-starter", "private": true, "scripts": {"start": "nodemon src/server.js", "dev": "nodemon src/server.js", "test": "jest"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.21.2", "express-validator": "^7.0.1", "google-auth-library": "^10.1.0", "googleapis": "^149.0.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "node-fetch": "^2.7.0", "nodemailer": "^6.10.0", "open": "^10.1.2", "strip": "^3.0.0", "stripe": "^18.1.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^6.3.3"}}